import { useState, useRef, useCallback, useEffect } from 'react'
import { NativeAudioManager } from '@/modules/video-editor/resource-plugin-system/components/audio-resource-item'

export function useAudioPlayer(isCached: boolean, id: string | number, getSrc: () => string | null) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [audioInitialized, setAudioInitialized] = useState(false)
  const [showProgressBar, setShowProgressBar] = useState(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioManager = NativeAudioManager.getInstance()

  // 音频事件处理函数
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) setCurrentTime(audioRef.current.currentTime)
  }, [])

  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) setDuration(audioRef.current.duration)
  }, [])

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false)
    setCurrentTime(0)
    setShowProgressBar(false)
    audioManager.stop(id)
  }, [id])

  const handleAudioError = useCallback(() => {
    setIsPlaying(false)
    setShowProgressBar(false)
    console.error('音频播放错误')
  }, [])

  // 音频管理器事件监听器
  const handleAudioManagerEvent = useCallback((event: 'play' | 'pause' | 'stop') => {
    switch (event) {
      case 'stop':
        setIsPlaying(false)
        setShowProgressBar(false)
        if (audioRef.current) audioRef.current.currentTime = 0
        setCurrentTime(0)
        break
      case 'pause':
        setIsPlaying(false)
        break
      case 'play':
        setIsPlaying(true)
        setShowProgressBar(true)
        break
    }
  }, [])

  // 初始化音频元素
  const initializeAudio = useCallback(() => {
    if (audioInitialized) return null
    const src = getSrc()
    if (!src) return null

    const audio = new Audio()
    audio.preload = 'metadata'
    audioRef.current = audio

    // 添加音频事件监听器
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('ended', handleAudioEnded)
    audio.addEventListener('error', handleAudioError)

    audio.src = src

    // 注册到音频管理器
    audioManager.registerListener(id, handleAudioManagerEvent)
    setAudioInitialized(true)

    return audio
  }, [audioInitialized, getSrc, isCached])

  // 播放/暂停切换
  const handleTogglePlay = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()

      // 如果音频尚未初始化，先初始化
      if (!audioInitialized) {
        const audio = initializeAudio()
        if (!audio) return
      }

      if (!audioRef.current) return

      if (isPlaying) {
        audioManager.pause(id)
        setIsPlaying(false)
        // 暂停时不隐藏进度条，保持显示状态
        // setShowProgressBar(false)
      } else {
        audioManager.play(audioRef.current, id)
        setIsPlaying(true)
        setShowProgressBar(true)
      }
    },
    [audioInitialized, initializeAudio, isPlaying, id],
  )

  // 组件清理
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        // 停止播放
        audioRef.current.pause()
        audioRef.current.src = ''
        audioRef.current.load()

        // 移除事件监听器
        audioRef.current.removeEventListener('timeupdate', handleTimeUpdate)
        audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata)
        audioRef.current.removeEventListener('ended', handleAudioEnded)
        audioRef.current.removeEventListener('error', handleAudioError)

        // 从音频管理器中注销
        audioManager.unregisterListener(id)
        audioManager.stop(id)
      }
    }
  }, [handleTimeUpdate, handleLoadedMetadata, handleAudioEnded, handleAudioError, id])

  return {
    isPlaying,
    currentTime,
    duration,
    showProgressBar,
    handleTogglePlay,
  }
}

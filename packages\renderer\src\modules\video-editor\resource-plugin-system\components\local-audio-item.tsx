import React, { ReactNode } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { useResourceCacheStatus } from '../../hooks/resource/useResourceCacheStatus'
import { useParseUrlFromObjectHref } from '@/hooks/useParseUrlFromObjectHref'
import { BaseAudioResourceCard, getAvailableSrcPath } from './audio-resource-item'
export interface LocalAudioResourceItemProps {
  /**
   * 音频地址
   */
  audioUrl: string
  /**
   * 音频时长
   */
  durations: number
  /**
   * 资源名
   */
  title: string
  /**
   * 资源id
   */
  id: string
  /**
   * 封面图
   */
  coverUrl?: string
  /**
   * 默认图标
   */
  icon?: ReactNode

  /**
   * 自定义内容
   */
  children?: ReactNode
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType: ResourceType
  /**
   * 自定义扩展名
   */
  customExt?: string
  /**
   * 是否是本地资源，非本地资源url需要重新获取
   */
  isLocal?: boolean
}

export function LocalAudioResourceItem({
  audioUrl,
  durations,
  title,
  id,
  coverUrl,
  icon,
  children,
  className = '',
  resourceType,
  isLocal = true,
}: LocalAudioResourceItemProps) {
  const { data: parsedUrl } = useParseUrlFromObjectHref(audioUrl)
  const resolvedAudioUrl = isLocal ? audioUrl : parsedUrl || audioUrl

  const thumbnailUrl = coverUrl || ''
  const description = (durations / 1000).toFixed(1) + 's'

  const { isCached } = useResourceCacheStatus(resourceType, resolvedAudioUrl)

  return (
    <div className={`aspect-square relative ${className}`}>
      <BaseAudioResourceCard
        id={id}
        title={title}
        description={description}
        thumbnailUrl={thumbnailUrl}
        icon={icon}
        isMaterial={!isLocal}
        getAudioSrc={() => getAvailableSrcPath(resolvedAudioUrl, resourceType, isCached)}
        isCached={isCached}
        resourceType={resourceType}
        audioUrl={resolvedAudioUrl}
      />

      {/* 子内容 */}
      {children}
    </div>
  )
}

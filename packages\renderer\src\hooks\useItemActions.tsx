import React from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { ResourceModule } from '@/libs/request/api/resource'
import { useDeleteModal } from '@/components/modal/delete'
import { toast } from 'react-toastify'
import { useCreateMaterial, useRenameMaterial } from '@/pages/Projects/material/components/model'
import { ResourceSource } from '@/types/resources'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { usePending } from './usePending'

const queryKeyMap: Record<ResourceSource, string> = {
  [ResourceSource.MEDIA]: QUERY_KEYS.MATERIAL_MEDIA_LIST,
  [ResourceSource.FOLDER]: QUERY_KEYS.MATERIAL_DIRECTORY_LIST,
  [ResourceSource.MULTI_SELECT]: QUERY_KEYS.MATERIAL_DIRECTORY_LIST,
  [ResourceSource.LOCAL_STICK_MULTI_SELECT]: QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST,
  [ResourceSource.LOCAL_MUSIC_MULTI_SELECT]: QUERY_KEYS.LOCAL_MUSIC_FOLDER_LIST,
  [ResourceSource.LOCAL_SOUND_MULTI_SELECT]: QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST,
  [ResourceSource.LOCAL_STICK]: QUERY_KEYS.LOCAL_PASTER_LIST,
  [ResourceSource.LOCAL_MUSIC]: QUERY_KEYS.LOCAL_MUSIC_LIST,
  [ResourceSource.LOCAL_SOUND]: QUERY_KEYS.LOCAL_SOUND_LIST,
  [ResourceSource.LOCAL_STICK_FOLDER]: QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST,
  [ResourceSource.LOCAL_MUSIC_FOLDER]: QUERY_KEYS.LOCAL_MUSIC_FOLDER_LIST,
  [ResourceSource.LOCAL_SOUND_FOLDER]: QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST,
}

const actionMessages = {
  move: '移动成功',
  recycleMulit: '已移入回收站',
  deleteMulitMulit: '删除成功',
}

/**
 * 操作对应的接口
 */
const resourceActions = {
  [ResourceSource.MEDIA]: {
    create: null,
    rename: (id: string, data: { title: string }) => ResourceModule.media.rename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.media.delete({ fileIds: [id] }),
    deleteMulit: (fileIds: string[]) => ResourceModule.media.delete({ fileIds }),
    move: (fileIds: string[], folderUuid: string) => ResourceModule.media.move({ fileIds, folderUuid }),
    recycle: (fileId: string) => ResourceModule.media.recycle({ fileIds: [fileId] }),
    recycleMulit: (fileIds: string[]) => ResourceModule.media.recycle({ fileIds }),
  },
  [ResourceSource.FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.directory.create({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.directory.rename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.directory.delete({ folderIds: [id] }),
    deleteMulit: (folderIds: string[]) => ResourceModule.directory.delete({ folderIds }),
    move: (folderIds: string[], parentId: string) => ResourceModule.directory.move({ folderIds, parentId }),
    recycle: (folderId: string) => ResourceModule.directory.recycle({ folderIds: [folderId] }),
    recycleMulit: (folderIds: string[]) => ResourceModule.directory.recycle({ folderIds }),
  },
  [ResourceSource.MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.directory.create({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: null,
  },
  [ResourceSource.LOCAL_STICK_MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.paster.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: (fileIds: string[], folderUuid: string) => ResourceModule.paster.localMove({ fileIds: fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_MUSIC_MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.music.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: (fileIds: string[], folderUuid: string) => ResourceModule.music.localMove({ fileIds: fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_SOUND_MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.voice.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: (fileIds: string[], folderUuid: string) => ResourceModule.voice.localMove({ fileIds: fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_STICK]: {
    create: null,
    rename: (id: string, data: { title: string }) =>
      ResourceModule.paster.localRename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.paster.localDelete({ fileIds: [id] }),
    deleteMulit: (ids: string[]) => ResourceModule.paster.localDelete({ fileIds: ids }),
    move: (fileIds: string[], folderUuid: string) => ResourceModule.paster.localMove({ fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_MUSIC]: {
    create: null,
    rename: (id: string, data: { title: string }) =>
      ResourceModule.music.localRename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.music.localDelete({ fileIds: [id] }),
    deleteMulit: (ids: string[]) => ResourceModule.music.localDelete({ fileIds: ids }),
    move: (fileIds: string[], folderUuid: string) => ResourceModule.music.localMove({ fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_SOUND]: {
    create: null,
    rename: (id: string, data: { title: string }) =>
      ResourceModule.voice.localRename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.voice.localDelete({ fileIds: [id] }),
    deleteMulit: (ids: string[]) => ResourceModule.voice.localDelete({ fileIds: ids }),
    move: (fileIds: string[], folderUuid: string) => ResourceModule.voice.localMove({ fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_STICK_FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.paster.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.paster.dirRename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.paster.dirDelete({ folderIds: [id] }),
    move: (folderIds: string[], parentId: string) => ResourceModule.paster.dirMove({ folderIds, parentId }),
  },
  [ResourceSource.LOCAL_MUSIC_FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.music.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.music.dirRename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.music.dirDelete({ folderIds: [id] }),
    move: (folderIds: string[], parentId: string) => ResourceModule.music.dirMove({ folderIds, parentId }),
  },
  [ResourceSource.LOCAL_SOUND_FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.voice.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.voice.dirRename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.voice.dirDelete({ folderIds: [id] }),
    move: (folderIds: string[], parentId: string) => ResourceModule.voice.dirMove({ folderIds, parentId }),
  },
} as const

/**
 * 文件夹：创建，重命名，删除，放入回收站
 * 媒体资源，我的音效，我的音乐，我的贴纸：重命名，删除
 */
export function useItemActions() {
  const queryClient = useQueryClient()
  const deleteModal = useDeleteModal()

  const executeAction = async (
    ResourceSource: ResourceSource,
    action: 'create' | 'rename' | 'delete' | 'move' | 'recycle' | 'deleteMulit' | 'recycleMulit',
    ...args: any[]
  ) => {
    const resourceAction = resourceActions[ResourceSource]
    if (!resourceAction || !resourceAction[action]) {
      throw new Error(`资源类型 ${ResourceSource} 不支持 ${action} 操作`)
    }
    return (resourceAction[action] as (...args: any[]) => Promise<any>)(...args)
  }

  const invalidate = async (types: ResourceSource[]) => {
    await Promise.all(
      types.map(type => {
        const queryKey = queryKeyMap[type]
        if (queryKey) {
          return queryClient.invalidateQueries({ queryKey: [queryKey] })
        }
      }),
    )
  }

  const createItem = useCreateMaterial(async (type, parentId, data) => {
    await executeAction(type, 'create', data, parentId)
    await invalidate([type])
    toast('创建成功', { type: 'success' })
  })

  const renameItem = useRenameMaterial(async (type, id, _title, data) => {
    await executeAction(type, 'rename', id, data)
    await invalidate([type])
    toast('重命名成功', { type: 'success' })
  })

  const deleteLocalItem = async (type: ResourceSource, nodeIds: string | string[], label: string) => {
    const isMulti = Array.isArray(nodeIds)
    deleteModal({
      kind: isMulti ? '所选' : '所选对象',
      name: label,
      danger: true,
      action: async () => {
        await executeAction(type, 'deleteMulit', isMulti ? nodeIds : [nodeIds])
        await invalidate([type])
        toast('删除成功', { type: 'success' })
      },
    })
  }

  const deleteItem = async (type: ResourceSource, nodeId: string, label: string) => {
    deleteModal({
      kind: '所选',
      name: label,
      danger: true,
      buttons: ({ close }) => {
        const { pending, withPending } = usePending()

        return (
          <>
            <Button
              variant="default"
              className="min-w-[80px] h-8 ml-2 bg-white/5 text-white border hover:bg-primary-highlight1"
              onClick={withPending(async () => {
                await executeAction(type, 'recycle', nodeId)
                await invalidate([type])
                toast('放入回收站成功', { type: 'success' })
                close()
              })}
            >
              {pending ? <Loader2 className="animate-spin size-4" /> : '放入回收站'}
            </Button>
            <Button
              variant="destructive"
              className="min-w-[80px] h-8 ml-2 bg-destructive text-white border hover:bg-destructive/90"
              onClick={withPending(async () => {
                await executeAction(type, 'delete', nodeId)
                await invalidate([type])
                toast('删除成功', { type: 'success' })
                close()
              })}
            >
              {pending ? <Loader2 className="animate-spin size-4" /> : '彻底删除'}
            </Button>
          </>
        )
      },
    })
  }

  const moveItem = async (type: ResourceSource, ids: string | string[], targetId: string) => {
    const isMulti = Array.isArray(ids)
    const idsArray = isMulti ? ids : [ids]

    // 校验：不能移动到自身
    if (type === ResourceSource.FOLDER && idsArray.includes(targetId)) {
      toast('不能将文件夹移动到自身', { type: 'warning' })
      return
    }

    await executeAction(type, 'move', idsArray, targetId)
    await invalidate([type])
    toast('移动成功', { type: 'success' })
  }

  // 素材库-多选文件和文件夹操作：移动/彻底删除/放入回收站
  const MediaAndFoldersMulitAction = async (
    action: 'move' | 'recycleMulit' | 'deleteMulit',
    selectedMediaItems: Set<string>,
    selectedFolderItems: Set<string>,
    resetSelection: () => void,
    targetFolderId?: string,
  ) => {
    if (selectedMediaItems.size === 0 && selectedFolderItems.size === 0) {
      toast('请先选择文件或文件夹', { type: 'info' })
      return
    }

    // 校验：移动时不能移动到自身
    if (
      action === 'move' &&
      selectedFolderItems.size > 0 &&
      targetFolderId &&
      selectedFolderItems.has(targetFolderId)
    ) {
      toast('不能将文件夹移动到自身', { type: 'warning' })
      return
    }

    await Promise.all([
      // 处理媒体文件
      selectedMediaItems.size > 0 &&
        (async () => {
          await executeAction(ResourceSource.MEDIA, action, Array.from(selectedMediaItems), targetFolderId)
          await invalidate([ResourceSource.MEDIA])
        })(),
      // 处理文件夹
      selectedFolderItems.size > 0 &&
        (async () => {
          await executeAction(ResourceSource.FOLDER, action, Array.from(selectedFolderItems), targetFolderId)
          await invalidate([ResourceSource.FOLDER])
        })(),
    ])

    resetSelection()
    toast(actionMessages[action] || '操作成功', { type: 'success' })
  }

  // 文件/文件夹/本地文件/本地文件夹拖拽移动到文件夹
  const handleResourceFolderOnDragEnd = async (
    draggable: ResourceSource, // 被拖动文件/文件夹类型
    droppable: ResourceSource, // 放置文件夹类型
    fileId: string, // 被拖动文件/文件夹id
    folderUuid: string, // 放置文件夹id
  ) => {
    if (!draggable || !droppable || !fileId || !folderUuid) return
    // 禁止移动到自身
    if (fileId === folderUuid) return

    try {
      // 执行移动操作
      console.log('执行移动操作')
      await executeAction(draggable, 'move', [fileId], folderUuid)
      if (draggable !== droppable) {
        await invalidate([draggable, droppable])
      } else {
        await invalidate([draggable])
      }
    } catch (err) {
      console.error('拖拽移动失败:', err)
    }

    toast('移动成功', { type: 'success' })
  }

  return {
    createItem,
    renameItem,
    deleteLocalItem,
    deleteItem,
    moveItem,
    MediaAndFoldersMulitAction,
    invalidate,
    handleResourceFolderOnDragEnd,
  }
}

import { forwardRef, Inject, Injectable, Logger, OnApplicationShutdown, OnModuleInit } from '@nestjs/common'
import { BrowserWindow } from 'electron'
import { fork, ChildProcess } from 'child_process'
import { dirname, join } from 'path'
import { fileURLToPath } from 'url'
import { cpus } from 'os'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { UploadTaskService } from './upload-task.service.js'
import { UploadTask } from '@app/shared/types/database.types.js'
import { FileUploaderService } from '../../file-uploader/file-uploader.service.js'

/**
 * Worker任务数据接口
 */
interface WorkerTaskData {
  taskId: number
  filePath: string
  fileName: string
  uploadConfig: {
    folderUuid?: string
    fileMd5?: string
    module: string
    partSize: number
    checkpointData?: string  // 断点续传数据
  }
}

/**
 * 上传队列管理器 - 基于Worker进程的轻量级任务协调器
 * 负责创建和管理Worker进程，处理任务分发
 */
@Injectable()
export class UploadQueueManager implements OnApplicationShutdown, OnModuleInit {

  private readonly logger = new Logger(UploadQueueManager.name)

  //#region 内部状态
  /**
   * 子进程池
   */
  private childProcesses: Map<string, ChildProcess> = new Map()

  /**
   * 任务到子进程的映射
   */
  private taskProcessMap: Map<number, string> = new Map()

  /**
   * 子进程最后使用时间跟踪
   */
  private processLastUsed: Map<string, number> = new Map()

  /**
   * 子进程当前任务数跟踪
   */
  private processTaskCount: Map<string, number> = new Map()

  /**
   * 队列处理器定时器
   */
  private queueProcessorTimer?: NodeJS.Timeout

  /**
   * 子进程清理定时器
   */
  private processCleanupTimer?: NodeJS.Timeout

  /**
   * 是否已初始化
   */
  private initialized = false

  /**
   * 最大子进程数量
   */
  private readonly MAX_PROCESSES = Math.min(cpus().length, 4)

  /**
   * 子进程空闲超时时间（5分钟）
   */
  private readonly PROCESS_IDLE_TIMEOUT = 5 * 60 * 1000

  /**
   * 子进程清理检查间隔（1分钟）
   */
  private readonly CLEANUP_INTERVAL = 60 * 1000

  /**
   * 进度事件批量发送的最大批量大小
   */
  private readonly MAX_BATCH_SIZE = 100

  /**
   * 进度事件批量发送的异常大小阈值（用于警告）
   */
  private readonly ABNORMAL_BATCH_SIZE = 500

  /**
   * 子进程脚本路径
   */
  private readonly workerScriptPath = this.getWorkerScriptPath()

  /**
   * 进度事件批量发送
   */
  private progressEventBatch: UploadTask.ProgressEvent[] = []
  private progressBatchTimer?: NodeJS.Timeout
  //#endregion

  onModuleInit(): any {
    // 测试子进程脚本是否可用
    try {
      const testProcess = fork(this.workerScriptPath)
      testProcess.kill()
      this.logger.debug(`成功从 ${this.workerScriptPath} 加载子进程脚本`)
    } catch (error) {
      this.logger.error(`无法加载子进程脚本: ${error}`)
    }

    // 设置队列管理器引用到服务中
    this.uploadTaskService.setQueueManager(this)

    // 延迟启动队列处理器，确保所有服务都已初始化
    this.scheduleQueueStart()

    // 启动子进程清理定时器
    this.startProcessCleanup()
  }

  constructor(
    @Inject(forwardRef(() => UploadTaskService)) private readonly uploadTaskService: UploadTaskService,
    @Inject(FileUploaderService) private readonly fileUploaderService: FileUploaderService
  ) {
  }

  /**
   * 获取子进程脚本路径
   * 现在直接使用主应用中编译的脚本文件
   */
  private getWorkerScriptPath(): string {
    // 直接使用编译后的 Worker 文件，无论开发还是生产环境
    return join(dirname(fileURLToPath(import.meta.url)), 'upload-worker.js')
  }

  /**
   * 调度队列启动，等待数据库准备就绪
   */
  private scheduleQueueStart(): void {
    const checkAndStart = () => {
      try {
        // 尝试启动队列处理器，如果数据库未准备好会抛出异常
        this.startQueueProcessor()
      } catch (error) {
        // 数据库还未准备好，继续等待
        setTimeout(checkAndStart, 2000)
      }
    }

    // 延迟开始检查
    setTimeout(checkAndStart, 5000)
  }

  /**
   * 启动队列处理器
   */
  startQueueProcessor(): void {
    if (this.initialized) {
      console.log('[UploadQueueManager] 队列处理器已经启动')
      return
    }

    console.log('[UploadQueueManager] 启动队列处理器')

    // 重置所有上传中的任务为等待状态
    // this.uploadTaskService.resetUploadingTasks()

    // 每3秒检查一次队列
    this.queueProcessorTimer = setInterval(() => {
      this.processQueue()
    }, 3000)

    this.initialized = true

    // 立即处理一次
    this.processQueue()
  }

  /**
   * 停止队列处理器
   */
  stopQueueProcessor(): void {
    if (this.queueProcessorTimer) {
      clearInterval(this.queueProcessorTimer)
      this.queueProcessorTimer = undefined
    }

    // 清理所有子进程
    for (const childProcess of this.childProcesses.values()) {
      childProcess.kill('SIGTERM')
    }
    this.childProcesses.clear()
    this.taskProcessMap.clear()

    this.initialized = false
  }

  /**
   * 处理上传队列
   */
  private async processQueue(): Promise<void> {
    try {
      const config = this.uploadTaskService.getQueueConfig()
      const currentActiveCount = this.taskProcessMap.size

      if (currentActiveCount >= config.max_concurrent_uploads) {
        return
      }

      const pendingTasks = this.uploadTaskService.getTasksByStatus(UploadTask.Status.PENDING)
      const availableSlots = config.max_concurrent_uploads - currentActiveCount

      const tasksToStart = pendingTasks.slice(0, availableSlots)

      for (const task of tasksToStart) {
        await this.startUpload(task)
      }
    } catch (error) {
      // 如果数据库尚未初始化，静默返回等待下次检查
      if (error instanceof Error && error.message.includes('数据库尚未初始化')) {
        return
      }
      console.error('[UploadQueueManager] 处理队列时出错:', error)
    }
  }

  /**
   * 开始上传任务
   */
  private async startUpload(task: UploadTaskModel): Promise<void> {
    try {
      if (this.taskProcessMap.has(task.id)) {
        console.log(`[UploadQueueManager] 任务 ${task.id} 已在上传中`)
        return
      }

      // 更新任务状态为上传中
      this.uploadTaskService.updateTaskStatus(task.id, UploadTask.Status.UPLOADING)

      // 创建或获取子进程
      const processId = await this.createWorker()
      const childProcess = this.childProcesses.get(processId)!

      // 记录任务到子进程的映射
      this.taskProcessMap.set(task.id, processId)

      // 增加子进程任务计数
      const currentCount = this.processTaskCount.get(processId) || 0
      this.processTaskCount.set(processId, currentCount + 1)

      // 准备上传数据
      const taskData: WorkerTaskData = {
        taskId: task.id,
        filePath: task.local_path,
        fileName: task.name,
        uploadConfig: {
          folderUuid: task.folder_id || undefined,
          fileMd5: task.hash || undefined,
          module: task.upload_module,
          partSize: 1024 * 1024, // 1MB
          checkpointData: task.checkpoint_data || undefined  // 传递断点续传数据
        }
      }

      // 发送上传任务到子进程
      if (childProcess.send) {
        childProcess.send({
          type: 'upload',
          data: taskData
        })
      }

      console.log(`[UploadQueueManager] 任务 ${task.id} 已分配给子进程 ${processId}`)
    } catch (error) {
      console.error(`[UploadQueueManager] 开始上传任务 ${task.id} 时出错:`, error)
      this.uploadTaskService.updateTaskStatus(task.id, UploadTask.Status.FAILED, error instanceof Error ? error.message : '上传失败')
      this.taskProcessMap.delete(task.id)
    }
  }

  /**
   * 创建或获取子进程
   */
  private async createWorker(): Promise<string> {
    // 首先尝试找到空闲的子进程（任务数为0的子进程）
    const idleProcessId = this.findIdleProcess()
    if (idleProcessId) {
      this.updateProcessUsage(idleProcessId)
      console.log(`[UploadQueueManager] 复用空闲子进程 ${idleProcessId}`)
      return idleProcessId
    }

    // 如果没有空闲子进程且未达到最大数量，创建新子进程
    if (this.childProcesses.size < this.MAX_PROCESSES) {
      const processId = this.generateProcessId()
      const childProcess = fork(this.workerScriptPath)

      // 设置子进程消息处理
      childProcess.on('message', message => {
        this.handleProcessMessage(processId, message)
      })

      childProcess.on('error', error => {
        console.error(`[UploadQueueManager] 子进程 ${processId} 错误:`, error)
        this.cleanupProcess(processId)
      })

      childProcess.on('exit', (code, signal) => {
        console.log(`[UploadQueueManager] 子进程 ${processId} 退出，代码: ${code}, 信号: ${signal}`)
        this.cleanupProcess(processId)
      })

      // 初始化子进程状态
      this.childProcesses.set(processId, childProcess)
      this.processTaskCount.set(processId, 0)
      this.updateProcessUsage(processId)

      console.log(`[UploadQueueManager] 创建新子进程 ${processId} (总数: ${this.childProcesses.size}/${this.MAX_PROCESSES})`)
      return processId
    }

    // 如果达到最大子进程数，选择任务数最少的子进程
    const leastBusyProcessId = this.findLeastBusyProcess()
    if (!leastBusyProcessId) {
      throw new Error('无法获取可用的子进程')
    }

    this.updateProcessUsage(leastBusyProcessId)
    console.log(`[UploadQueueManager] 分配给最空闲子进程 ${leastBusyProcessId}`)
    return leastBusyProcessId
  }

  /**
   * 处理子进程消息
   */
  private handleProcessMessage(processId: string, message: any): void {
    const { type, taskId, data } = message

    switch (type) {
      case 'progress':
        this.handleProgressUpdate(taskId, data.progress, data.checkpoint)
        break

      case 'complete':
        this.handleUploadComplete(taskId, data)
        break

      case 'error':
        this.handleUploadError(taskId, data.error)
        break

      case 'paused':
        this.handleUploadPaused(taskId, data.checkpoint)
        break

      case 'resumed':
        this.handleUploadResumed(taskId, data.checkpoint)
        break

      case 'request-oss-signature':
        this.handleOSSSignatureRequest(processId, message.requestId, data)
        break

      default:
        console.warn(`[UploadQueueManager] 未知的子进程消息类型: ${type}`)
    }
  }

  /**
   * 处理OSS签名请求
   */
  private async handleOSSSignatureRequest(processId: string, requestId: string, data: any): Promise<void> {
    try {
      const signature = await this.getOSSSignature(data)
      const childProcess = this.childProcesses.get(processId)
      if (childProcess && childProcess.send) {
        childProcess.send({
          type: 'oss-signature-response',
          requestId,
          success: true,
          data: signature
        })
      }
    } catch (error) {
      console.error('[UploadQueueManager] 获取OSS签名失败:', error)
      const childProcess = this.childProcesses.get(processId)
      if (childProcess && childProcess.send) {
        childProcess.send({
          type: 'oss-signature-response',
          requestId,
          success: false,
          error: error instanceof Error ? error.message : '获取OSS签名失败'
        })
      }
    }
  }

  /**
   * 获取OSS签名 - 复用FileUploaderService的逻辑
   */
  private async getOSSSignature(params: any): Promise<any> {
    // 使用FileUploaderService的generateSTSUrl方法
    const signature = await (this.fileUploaderService as any).generateSTSUrl(
      params.fileName,
      params.folderUuid || '',
      params.fileMd5,
      params.module
    )

    if (signature instanceof Error) {
      throw signature
    }

    return signature
  }

  /**
   * 处理进度更新
   */
  private handleProgressUpdate(taskId: number, progress: number, checkpoint?: any): void {
    console.log(`[UploadQueueManager] 接收到进度更新: 任务${taskId}, 进度${(progress * 100).toFixed(1)}%`)

    // 获取当前任务状态
    const task = this.uploadTaskService.findById(taskId)
    if (task && task.status === UploadTask.Status.PENDING && progress > 0) {
      // 如果任务状态还是 PENDING 但已经有进度，更新状态为 UPLOADING
      console.log(`[UploadQueueManager] 任务${taskId} 状态从 PENDING 更新为 UPLOADING`)
      this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.UPLOADING)
    }

    // 更新数据库中的进度
    this.uploadTaskService.update(taskId, {
      progress: progress,
      ...(checkpoint ? { checkpoint_data: JSON.stringify(checkpoint) } : {})
    })

    // 检查批量数组是否异常大，记录警告
    if (this.progressEventBatch.length >= this.ABNORMAL_BATCH_SIZE) {
      console.warn(`[UploadQueueManager] 进度事件批量数组异常大: ${this.progressEventBatch.length} 个事件，可能存在内存泄漏`)
    }

    const existingEventIndex = this.progressEventBatch.findIndex(event => event.task_id === taskId)
    if (existingEventIndex !== -1) {
      this.progressEventBatch[existingEventIndex].progress = progress
    } else {
      this.progressEventBatch.push({
        task_id: taskId,
        progress: progress
      })
    }

    // 如果达到最大批量大小，立即发送，不等待定时器
    if (this.progressEventBatch.length >= this.MAX_BATCH_SIZE) {
      this.sendBatchProgressEvents()
      return
    }

    // 如果还没有批量发送定时器，创建一个作为兜底机制
    if (!this.progressBatchTimer) {
      this.progressBatchTimer = setTimeout(() => {
        this.sendBatchProgressEvents()
      }, 500)
    }
  }

  /**
   * 批量发送进度事件
   */
  private sendBatchProgressEvents(): void {
    // 清理定时器
    if (this.progressBatchTimer) {
      clearTimeout(this.progressBatchTimer)
      this.progressBatchTimer = undefined
    }

    const batchSize = this.progressEventBatch.length
    if (batchSize === 0) {
      return
    }

    // 检查是否为异常大的批量，记录警告
    if (batchSize >= this.ABNORMAL_BATCH_SIZE) {
      console.warn(`[UploadQueueManager] 发送异常大的进度事件批量: ${batchSize} 个事件`)
    }

    try {
      // 创建事件副本，避免在发送过程中被修改
      const eventsToSend = [...this.progressEventBatch]

      // 立即清空批量数组，确保即使发送失败也不会导致内存泄漏
      this.progressEventBatch = []

      // 发送到所有窗口
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach(window => {
        try {
          window.webContents.send('batch-upload-progress', eventsToSend)
        } catch (error) {
          console.error('[UploadQueueManager] 向窗口发送进度事件失败:', error)
        }
      })
    } catch (error) {
      console.error('[UploadQueueManager] 批量发送进度事件失败:', error)
      // 即使发送失败，数组也已经被清空，避免内存泄漏
    }
  }

  /**
   * 处理上传完成
   */
  private handleUploadComplete(taskId: number, data: any): void {
    this.uploadTaskService.update(taskId, {
      url: data.url,
      object_id: data.objectId || '',
      object_key: data.fileName || '',
      progress: 1.0,
      status: UploadTask.Status.COMPLETED,
      checkpoint_data: ''
    })

    // 减少子进程任务计数
    const processId = this.taskProcessMap.get(taskId)
    if (processId) {
      const currentCount = this.processTaskCount.get(processId) || 0
      this.processTaskCount.set(processId, Math.max(0, currentCount - 1))
    }

    this.taskProcessMap.delete(taskId)
    console.log(`[UploadQueueManager] 任务 ${taskId} 上传完成: ${data.url}`)
  }

  /**
   * 处理上传错误
   */
  private handleUploadError(taskId: number, error: string): void {
    this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.FAILED, error)

    // 减少子进程任务计数
    const processId = this.taskProcessMap.get(taskId)
    if (processId) {
      const currentCount = this.processTaskCount.get(processId) || 0
      this.processTaskCount.set(processId, Math.max(0, currentCount - 1))
    }

    this.taskProcessMap.delete(taskId)
    console.error(`[UploadQueueManager] 任务 ${taskId} 上传失败: ${error}`)
  }

  /**
   * 处理上传暂停
   */
  private handleUploadPaused(taskId: number, checkpoint?: any): void {
    // 更新任务状态为暂停，并保存 checkpoint 数据
    this.uploadTaskService.update(taskId, {
      status: UploadTask.Status.PAUSED,
      checkpoint_data: checkpoint ? JSON.stringify(checkpoint) : ''
    })

    console.log(`[UploadQueueManager] 任务 ${taskId} 已暂停${checkpoint ? '，已保存断点数据' : ''}`)
  }

  /**
   * 处理上传恢复
   */
  private async handleUploadResumed(taskId: number, _checkpoint?: any): Promise<void> {
    console.log(`[UploadQueueManager] 任务 ${taskId} 请求恢复`)

    // 清理当前的子进程映射，因为需要重新启动上传
    const processId = this.taskProcessMap.get(taskId)
    if (processId) {
      this.taskProcessMap.delete(taskId)
      const currentCount = this.processTaskCount.get(processId) || 0
      this.processTaskCount.set(processId, Math.max(0, currentCount - 1))
    }

    // 更新任务状态为等待，让队列处理器重新处理
    this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING)

    console.log(`[UploadQueueManager] 任务 ${taskId} 已标记为等待重新上传`)
  }

  /**
   * 清理子进程（异常退出时调用）
   */
  private cleanupProcess(processId: string): void {
    console.log(`[UploadQueueManager] 清理异常退出的子进程 ${processId}`)

    // 清理子进程相关数据
    this.childProcesses.delete(processId)
    this.processLastUsed.delete(processId)
    this.processTaskCount.delete(processId)

    // 清理相关的任务映射
    for (const [taskId, mappedProcessId] of this.taskProcessMap) {
      if (mappedProcessId === processId) {
        this.taskProcessMap.delete(taskId)
        // 将任务状态重置为等待，以便重新分配
        this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING, '子进程异常退出')
      }
    }
  }

  /**
   * 暂停上传任务
   */
  async pauseUpload(taskId: number): Promise<boolean> {
    console.log(`[UploadQueueManager] 接收到暂停任务请求: ${taskId}`)

    const processId = this.taskProcessMap.get(taskId)
    if (processId) {
      const childProcess = this.childProcesses.get(processId)
      if (childProcess && childProcess.send) {
        console.log(`[UploadQueueManager] 向子进程 ${processId} 发送暂停消息`)
        childProcess.send({
          type: 'pause',
          taskId
        })
        return true
      } else {
        console.warn(`[UploadQueueManager] 子进程 ${processId} 不存在`)
      }
    } else {
      console.warn(`[UploadQueueManager] 任务 ${taskId} 没有对应的子进程，直接更新状态`)
      // 如果任务不在子进程中，直接更新状态为暂停
      this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PAUSED)
      return true
    }
    return false
  }

  /**
   * 恢复上传任务
   */
  async resumeUpload(taskId: number): Promise<boolean> {
    console.log(`[UploadQueueManager] 接收到恢复任务请求: ${taskId}`)

    const processId = this.taskProcessMap.get(taskId)
    if (processId) {
      const childProcess = this.childProcesses.get(processId)
      if (childProcess && childProcess.send) {
        console.log(`[UploadQueueManager] 向子进程 ${processId} 发送恢复消息`)
        childProcess.send({
          type: 'resume',
          taskId
        })
        return true
      } else {
        console.warn(`[UploadQueueManager] 子进程 ${processId} 不存在`)
      }
    } else {
      console.log(`[UploadQueueManager] 任务 ${taskId} 没有对应的子进程，直接重新启动`)
      // 如果任务不在子进程中，直接更新状态为等待，让队列处理器重新处理
      this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING)
      return true
    }
    return false
  }

  /**
   * 取消上传任务
   */
  async cancelUpload(taskId: number): Promise<boolean> {
    const processId = this.taskProcessMap.get(taskId)
    if (processId) {
      const childProcess = this.childProcesses.get(processId)
      if (childProcess && childProcess.send) {
        childProcess.send({
          type: 'cancel',
          taskId
        })
        this.taskProcessMap.delete(taskId)
        return true
      }
    }
    return false
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    initialized: boolean
    activeProcesses: number
    activeTasks: number
    maxProcesses: number
    processTaskCounts: Record<string, number>
    progressBatchStatus: {
      currentSize: number
      maxSize: number
      hasTimer: boolean
    }
  } {
    const processTaskCounts: Record<string, number> = {}
    for (const [processId, count] of this.processTaskCount) {
      processTaskCounts[processId] = count
    }

    return {
      initialized: this.initialized,
      activeProcesses: this.childProcesses.size,
      activeTasks: this.taskProcessMap.size,
      maxProcesses: this.MAX_PROCESSES,
      processTaskCounts,
      progressBatchStatus: {
        currentSize: this.progressEventBatch.length,
        maxSize: this.MAX_BATCH_SIZE,
        hasTimer: !!this.progressBatchTimer
      }
    }
  }

  /**
   * 启动子进程清理定时器
   */
  private startProcessCleanup(): void {
    this.processCleanupTimer = setInterval(() => {
      this.cleanupIdleProcesses()
    }, this.CLEANUP_INTERVAL)

    console.log(`[UploadQueueManager] 子进程清理定时器已启动，间隔: ${this.CLEANUP_INTERVAL}ms`)
  }

  /**
   * 清理空闲的子进程
   */
  private cleanupIdleProcesses(): void {
    const now = Date.now()
    const processesToCleanup: string[] = []

    // 找出需要清理的空闲子进程
    for (const [processId, lastUsed] of this.processLastUsed) {
      const taskCount = this.processTaskCount.get(processId) || 0
      const idleTime = now - lastUsed

      // 如果子进程空闲且超过超时时间，标记为清理
      if (taskCount === 0 && idleTime > this.PROCESS_IDLE_TIMEOUT) {
        processesToCleanup.push(processId)
      }
    }

    // 清理空闲子进程，但至少保留一个子进程
    for (const processId of processesToCleanup) {
      if (this.childProcesses.size <= 1) {
        break // 保留最后一个子进程
      }
      this.terminateProcess(processId)
    }

    if (processesToCleanup.length > 0) {
      console.log(`[UploadQueueManager] 清理了 ${processesToCleanup.length} 个空闲子进程，剩余: ${this.childProcesses.size}`)
    }
  }

  /**
   * 查找空闲的子进程（任务数为0）
   */
  private findIdleProcess(): string | null {
    for (const [processId, taskCount] of this.processTaskCount) {
      if (taskCount === 0) {
        return processId
      }
    }
    return null
  }

  /**
   * 查找任务数最少的子进程
   */
  private findLeastBusyProcess(): string | null {
    let leastBusyProcessId: string | null = null
    let minTaskCount = Infinity

    for (const [processId, taskCount] of this.processTaskCount) {
      if (taskCount < minTaskCount) {
        minTaskCount = taskCount
        leastBusyProcessId = processId
      }
    }

    return leastBusyProcessId
  }

  /**
   * 生成子进程 ID
   */
  private generateProcessId(): string {
    return `process_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 更新子进程使用时间
   */
  private updateProcessUsage(processId: string): void {
    this.processLastUsed.set(processId, Date.now())
  }

  /**
   * 终止子进程
   */
  private terminateProcess(processId: string): void {
    const childProcess = this.childProcesses.get(processId)
    if (childProcess) {
      console.log(`[UploadQueueManager] 终止子进程 ${processId}`)

      // 终止子进程
      childProcess.kill('SIGTERM')

      // 清理相关数据
      this.childProcesses.delete(processId)
      this.processLastUsed.delete(processId)
      this.processTaskCount.delete(processId)

      // 清理相关的任务映射
      const affectedTasks: number[] = []
      for (const [taskId, mappedProcessId] of this.taskProcessMap) {
        if (mappedProcessId === processId) {
          this.taskProcessMap.delete(taskId)
          affectedTasks.push(taskId)
          // 将任务状态重置为等待，以便重新分配
          this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING, '子进程被清理')
        }
      }

      if (affectedTasks.length > 0) {
        console.log(`[UploadQueueManager] 子进程 ${processId} 清理完成，影响任务: ${affectedTasks.join(', ')}`)
      }
    }
  }

  /**
   * NestJS 应用关闭时的生命周期钩子
   */
  async onApplicationShutdown(): Promise<void> {
    this.destroy()
  }

  /**
   * 销毁队列管理器，清理所有资源
   */
  destroy(): void {
    console.log('[UploadQueueManager] 开始销毁队列管理器')

    // 清理定时器
    if (this.queueProcessorTimer) {
      clearInterval(this.queueProcessorTimer)
      this.queueProcessorTimer = undefined
    }

    if (this.processCleanupTimer) {
      clearInterval(this.processCleanupTimer)
      this.processCleanupTimer = undefined
    }

    if (this.progressBatchTimer) {
      clearTimeout(this.progressBatchTimer)
      this.progressBatchTimer = undefined
    }

    // 如果还有未发送的进度事件，记录警告并清理
    if (this.progressEventBatch.length > 0) {
      console.warn(`[UploadQueueManager] 销毁时清理了 ${this.progressEventBatch.length} 个未发送的进度事件`)
      this.progressEventBatch = []
    }

    // 终止所有子进程
    const processIds = Array.from(this.childProcesses.keys())
    for (const processId of processIds) {
      this.terminateProcess(processId)
    }

    // 清理所有状态
    this.childProcesses.clear()
    this.taskProcessMap.clear()
    this.processLastUsed.clear()
    this.processTaskCount.clear()

    this.initialized = false
    console.log('[UploadQueueManager] 队列管理器已销毁')
  }
}

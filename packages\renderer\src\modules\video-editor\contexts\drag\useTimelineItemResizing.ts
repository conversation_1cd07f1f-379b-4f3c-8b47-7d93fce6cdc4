import { useCallback } from 'react'
import { useEditorContext, useTimelineContext } from '@/modules/video-editor/contexts'
import { DraggableState, OverlayDragInfo, useDragContext } from '@/modules/video-editor/contexts/drag/drag.context'
import { byStartFrame, findTrackByOverlay } from '@/modules/video-editor/utils/overlay-helper'
import { findStoryboardByFromFrame } from '@/modules/video-editor/utils/track-helper'
import { snapToGrid } from '@/modules/video-editor/contexts/drag/drag.utils'
import { Overlay } from '@clipnest/remotion-shared/types'
import { AdjustCalculator } from '@/modules/video-editor/contexts/drag/adjust-calculator'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { Track } from '@/modules/video-editor/types'

function calculateDraggableState(
  tracks: Track[],
  currentOverlay: Overlay,
  intendedNewDuration: number,
): DraggableState {
  if (!currentOverlay) {
    return {
      draggable: false,
    }
  }

  const originalTrack = findTrackByOverlay(tracks, currentOverlay.id) || null
  if (!originalTrack) {
    return { draggable: false }
  }

  const calculator = new AdjustCalculator(tracks)
  const targetStoryboard = originalTrack?.isGlobalTrack
    ? null
    : findStoryboardByFromFrame(tracks, currentOverlay.from)

  if (!originalTrack) {
    return {
      draggable: false
    }
  }

  const restOverlays = originalTrack
    .overlays
    .filter(o => (
      currentOverlay.storyboardIndex === undefined
      || o.storyboardIndex === currentOverlay.storyboardIndex
    ))
    .filter(o => o.id !== currentOverlay.id)
    .sort(byStartFrame())

  return {
    draggable: true,
    adjustedStartFrame: currentOverlay.from,
    ...calculator.calcAdjustForResize(
      currentOverlay,
      restOverlays,
      intendedNewDuration,
      targetStoryboard,
      targetStoryboard !== null
    )
  }
}

/**
 * 处理 TimelineItem 调整时长
 */
export const useTimelineItemResizing = () => {
  const { zoomScale } = useTimelineContext()
  const { tracks } = useEditorContext()
  const { dragInfoRef, updateDraggableState } = useDragContext()

  const calculateTargetDuration = useCallback((
    dragInfo: OverlayDragInfo,
    deltaX: number,
  ) => {
    const deltaFrame = snapToGrid(deltaX / zoomScale / PIXELS_PER_FRAME)

    const targetDuration: number = Math.max(1,  dragInfo.initialDurationInFrames + deltaFrame)

    return targetDuration
  }, [zoomScale])

  const handleResizeMove = useCallback(
    (deltaX: number) => {
      if (!dragInfoRef.current) return

      const targetDuration = calculateTargetDuration(
        dragInfoRef.current,
        deltaX,
      )

      dragInfoRef.current.currentDuration = targetDuration

      updateDraggableState(
        calculateDraggableState(
          tracks,
          dragInfoRef.current.overlay,
          targetDuration,
        )
      )
    },
    [tracks, calculateTargetDuration],
  )

  return {
    handleResizeMove,
  }
}

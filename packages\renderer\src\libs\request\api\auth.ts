import { CodeLoginParams, SendSMSCodeParams } from '@/types/auth'
import request from '../request'
import { AuthState } from '@app/shared/types/auth-request.types'

export const AuthModule = {
  sendSMSCode(data: SendSMSCodeParams) {
    return request.post('/app-api/creative/auth/send-sms-code', data)
  },
  codeLogin(data: CodeLoginParams) {
    return request.post<AuthState>('/app-api/creative/auth/sms-login', data)
  }
}
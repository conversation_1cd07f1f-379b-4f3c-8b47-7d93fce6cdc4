import React from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'

import { OverlayUpdater } from '@/modules/video-editor/contexts/cached-overlays/context'

export type OverlayEditingContextValues<TOverlay extends Overlay> = {
  localOverlay: TOverlay,
  updateEditingOverlay(
    overlay: OverlayUpdater<TOverlay>,
    commit?: boolean
  ): void
}

export const OverlayEditingContext = React.createContext<OverlayEditingContextValues<any>>(null as any)

export function useOverlayEditing<TOverlay extends Overlay = Overlay>() {
  const context = React.useContext(OverlayEditingContext) as OverlayEditingContextValues<TOverlay> | null
  if (!context) {
    throw new Error('useOverlaySetting must be used within an OverlaySettingContext')
  }

  const { localOverlay, updateEditingOverlay } = context

  return {
    localOverlay,
    updateEditingOverlay
  }
}

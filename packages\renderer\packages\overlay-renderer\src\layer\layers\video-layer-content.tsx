import { continueRender, delayRender, OffthreadVideo } from 'remotion'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import React, { useEffect, useMemo } from 'react'
import { toAbsoluteUrl } from '@clipnest/remotion-shared/utils'
import { useOverlayAnimation, useOverlayFadeOpacityMultiplier } from '../../hooks'

interface VideoLayerContentProps {
  overlay: VideoOverlay
  baseUrl?: string
}

function getVideoSrc(overlay: VideoOverlay, baseUrl?: string) {
  // Determine the video source URL
  let videoSrc = overlay.localSrc || overlay.src

  // If it's a relative URL and baseUrl is provided, use baseUrl
  if (overlay.src.startsWith('/') && baseUrl) {
    videoSrc = `${baseUrl}${overlay.src}`
  }
  // Otherwise use the toAbsoluteUrl helper for relative URLs
  else if (overlay.src.startsWith('/')) {
    videoSrc = toAbsoluteUrl(overlay.src)
  }

  return videoSrc
}

export const VideoLayerContent: React.FC<VideoLayerContentProps> = ({
  overlay, baseUrl,
}) => {
  const animation = useOverlayAnimation(overlay)
  const opacityMultiplier = useOverlayFadeOpacityMultiplier(overlay)
  const videoSrc = getVideoSrc(overlay, baseUrl)

  useEffect(() => {
    const handle = delayRender('Loading video')

    const video = document.createElement('video')
    video.src = videoSrc

    const handleLoadedMetadata = () => {
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('error', handleError)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [])

  const cropStyles = useMemo((): React.CSSProperties => {
    if (!overlay.cropData) return {}

    const { x, y, width, height } = overlay.cropData

    const scaleX = 100 / width * 100
    const scaleY = 100 / height * 100
    const translateX = -x
    const translateY = -y

    return {
      width: `${scaleX}%`,
      height: `${scaleY}%`,
      transform: `translate(${translateX}%, ${translateY}%)`,
      transformOrigin: '0 0'
    }
  }, [overlay.cropData])

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: overlay.styles.paddingBackgroundColor || 'transparent',
    opacity: (overlay.styles.opacity ?? 1) * opacityMultiplier,
  }

  const baseTransform = overlay.styles.transform || 'none'
  const padding = overlay.styles.padding || 0
  const paddingContainerStyle: React.CSSProperties = {
    position: 'relative',
    width: `${100 - padding}%`,
    height: `${100 - padding}%`,
    overflow: 'hidden',
    transform: baseTransform
  }

  const videoStyle: React.CSSProperties = {
    width: cropStyles.width,
    height: cropStyles.height,
    maxWidth: 'unset',
    objectFit: overlay.styles.objectFit || 'cover',
    position: 'absolute',
    left: 0,
    top: 0,
    transform: cropStyles.transform,
    transformOrigin: cropStyles.transformOrigin || 'center',
    borderRadius: overlay.styles.borderRadius || '0px',
    filter: overlay.styles.filter || 'none',
    boxShadow: overlay.styles.boxShadow || 'none',
    border: overlay.styles.border || 'none',
    ...animation,
    transition: 'opacity 0.1s ease-in-out',
  }

  return (
    <div style={containerStyle} className="video-layer-content-container">
      <div style={paddingContainerStyle}>
        <OffthreadVideo
          src={videoSrc}
          startFrom={overlay.trimStartFrames}
          endAt={overlay.originalDurationInFrames - (overlay.trimEndFrames ?? 0)}
          style={videoStyle}
          className="video-layer-content-video"
          volume={overlay.styles.volume ?? 1}
          playbackRate={overlay.speed ?? 1}
        />
      </div>
    </div>
  )
}

import React, { <PERSON><PERSON><PERSON>ith<PERSON>hildren, useCallback, useState } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  pointerWithin,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import { Overlay } from '@clipnest/remotion-shared/types'
import { CloudResourceTypes, MaterialResource, ResourceSource } from '@/types/resources'
import { IndexableTrack } from '@/modules/video-editor/types'
import folderIcon from '@/assets/folder.svg'
import { useItemActions } from '@/hooks/useItemActions'
import { AuthedImg } from '@/components/authed-img'
import { omit } from 'lodash'
import { EnhancedTextRenderer } from '@/modules/video-editor/components/common/enhanced-layer-text-renderer'
import { buildTextOverlay } from '@/modules/video-editor/utils/text'
import { useTimelineItemMoving } from '@/modules/video-editor/contexts/drag/useTimelineItemMoving'
import { useResourceDrag } from '@/modules/video-editor/contexts/drag/useResourceDrag'
import { DroppableResource } from '@/modules/video-editor/contexts/drag/drag.utils'
import { useDragContext } from '@/modules/video-editor/contexts/drag/drag.context'

export enum EditorDraggableTypes {
  TimelineItem = 'TimelineItem',
  Resource = 'Resource',
}

export enum EditorDroppableTypes {
  TimelineTrack = 'TimelineTrack',
  Folder = 'Folder',
}

type PayloadTypeByDraggableType = {
  [EditorDraggableTypes.TimelineItem]: {
    type: EditorDraggableTypes.TimelineItem
    overlay: Overlay
  }
  [EditorDraggableTypes.Resource]: {
    type: EditorDraggableTypes.Resource
  } & DroppableResource
}

type PayloadTypeByDroppableType = {
  [EditorDroppableTypes.TimelineTrack]: {
    type: EditorDroppableTypes.TimelineTrack
    track: IndexableTrack
  }
  [EditorDroppableTypes.Folder]: {
    type: EditorDroppableTypes.Folder
    resource: MaterialResource.Media & { materialType: ResourceSource }
  }
}

type ValueOf<T> = T[keyof T]
type DraggablePayload = ValueOf<PayloadTypeByDraggableType>
type DroppablePayload = ValueOf<PayloadTypeByDroppableType>

type TransformedDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent> = {
  event: Event & { activatorEvent: PointerEvent }
  draggable: DraggablePayload
  droppable?: DroppablePayload
}

function transformDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent>(
  event: Event,
): TransformedDragEvent<Event> {
  const draggable = event.active.data.current as DraggablePayload

  const droppable = 'over' in event ? (event.over?.data.current as DroppablePayload | undefined) : undefined

  return { event: event as any, draggable, droppable }
}

type DraggingResourceData = {
  x: number
  y: number
  dx: number
  dy: number
  resource: DroppableResource
}

const DraggingResource: React.FC<DraggingResourceData> = ({ x, y, dy, dx, resource }) => {
  const contentForMaterial = (data: MaterialResource.Media) => (
    <>
      {data.resType === MaterialResource.MediaType.FOLDER
        ? <img src={folderIcon} alt="文件夹" />
        : data.resType === MaterialResource.MediaType.AUDIO
          ? <AuthedImg src={data.cover} alt={data.fileName} />
          : data.cover
            ? <AuthedImg src={data.cover} alt={data.fileName} />
            : null}
    </>
  )

  return (
    <div
      style={{
        position: 'fixed',
        left: x + dx,
        top: y + dy,
        width: 60,
        height: 40,
        zIndex: 9999, // 确保拖拽元素在最上层
        pointerEvents: 'none', // 避免拖拽元素阻挡鼠标事件
      }}
    >
      {
        resource.resourceType === CloudResourceTypes.MATERIAL
          ? contentForMaterial(resource.data)
          : resource.resourceType === CloudResourceTypes.PASTER
            ? <AuthedImg src={resource.data.content.thumbUrl} alt={resource.data.id.toString()} />
            : resource.resourceType === CloudResourceTypes.SOUND
              ? <AuthedImg src={resource.data.cover?.url} alt={resource.data.title} />
              : resource.resourceType === CloudResourceTypes.STYLED_TEXT
                ? (
                  <EnhancedTextRenderer
                    overlay={buildTextOverlay(resource.data, { isPreview: true, textContent: '花字' })}
                    containerStyle={{}}
                    isPreview={true}
                  />
                )
                : null
      }
    </div>
  )
}

export function useTypedDraggable<T extends EditorDraggableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDraggableType[T], 'type'>,
  options?: { disabled?: boolean }
) {
  return useDraggable({
    id: `${type}-${id}`,
    data: {
      ...payload,
      type,
    },
    disabled: options?.disabled,
  })
}

export function useTypedDroppable<T extends EditorDroppableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDroppableType[T], 'type'>,
) {
  return useDroppable({
    id: `${type}-${id}`,
    data: {
      ...payload,
      type,
    },
  })
}

export const EditorDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8
      }
    })
  )

  const { handleOverlayDragMove } = useTimelineItemMoving()
  const { handleTimelineItemAdjustStart, handleTimelineItemAdjustEnd } = useDragContext()
  const { handleResourceDragStart, handleResourceDragMove, handleResourceDragEnd, } = useResourceDrag()

  const { handleResourceFolderOnDragEnd } = useItemActions()

  const [draggingResource, setDraggingResource] = useState<DraggingResourceData | null>(null)

  const handleDragStart = useCallback(
    ({ draggable, event }: TransformedDragEvent<DragStartEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        const { overlay } = draggable
        return handleTimelineItemAdjustStart(overlay)
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        const resource = omit(draggable, 'type') as DroppableResource

        if (resource) {
          setDraggingResource({
            resource,
            x: event.activatorEvent.clientX,
            y: event.activatorEvent.clientY,
            dx: 0,
            dy: 0,
          })
          handleResourceDragStart(resource, event.activatorEvent.clientX)
        }
      }
    },
    [handleTimelineItemAdjustStart, handleResourceDragStart],
  )

  const handleDragMove = useCallback(
    ({ droppable, event, draggable }: TransformedDragEvent<DragMoveEvent>) => {
      // 处理时间轴中 `TimelineItem` 拖动的情况
      if (
        draggable.type === EditorDraggableTypes.TimelineItem &&
        droppable?.type === EditorDroppableTypes.TimelineTrack
      ) {
        const { x: deltaX } = event.delta
        const targetTrackIndex = droppable.track.index
        return handleOverlayDragMove(deltaX, targetTrackIndex)
      }

      // 处理左侧资源栏 (资源库/贴纸/音效/音乐/文字) 拖动的情况
      if (draggable.type === EditorDraggableTypes.Resource) {
        const currentMouseX = event.activatorEvent.clientX + event.delta.x
        const targetTrack = droppable?.type === EditorDroppableTypes.TimelineTrack
          ? droppable.track
          : undefined

        handleResourceDragMove(currentMouseX, targetTrack)

        setDraggingResource(prev =>
          prev && targetTrack === undefined
            ? {
              ...prev,
              dx: event.delta.x,
              dy: event.delta.y,
            }
            : null,
        )
      }
    },
    [handleOverlayDragMove, handleResourceDragMove],
  )

  const handleDragEnd = useCallback(
    ({ draggable, droppable }: TransformedDragEvent<DragEndEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        return handleTimelineItemAdjustEnd()
      }

      // 处理 "资源库" 面板中, 将资源/文件夹拖拽到文件夹中的动作
      if (
        droppable?.type === EditorDroppableTypes.Folder
        && draggable.type === EditorDraggableTypes.Resource
        && draggable.resourceType === CloudResourceTypes.MATERIAL
      ) {
        setDraggingResource(null)
        return handleResourceFolderOnDragEnd(
          draggable.data.materialType,
          droppable.resource.materialType,
          draggable.data.fileId,
          droppable.resource.fileId,
        )
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        setDraggingResource(null)
        handleResourceDragEnd()
        return
      }

      // 移到空白处也要清空预览图片
      setDraggingResource(null)
    },
    [handleTimelineItemAdjustEnd, handleResourceDragEnd, handleResourceFolderOnDragEnd],
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={pointerWithin}
      onDragStart={v => handleDragStart(transformDragEvent(v))}
      onDragMove={v => handleDragMove(transformDragEvent(v))}
      onDragEnd={v => handleDragEnd(transformDragEvent(v))}
    >
      {children}

      {draggingResource && <DraggingResource {...draggingResource} />}
    </DndContext>
  )
}

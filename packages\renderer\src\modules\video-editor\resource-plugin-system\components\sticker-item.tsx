import React, { FC, memo, useCallback, useEffect, useRef, useState } from 'react'
import { CloudResourceTypes, PasterResource } from '@/types/resources'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { useResource } from '../../hooks/resource/useResource'
import { selectStickerState, useStickerLoadingStore } from '../../hooks/resource/useStickerLoadingStore'
import { ResourceCacheIndicator } from '@/modules/video-editor/resource-plugin-system/components/resource-cache-indicator'
import { ResourceCollectionIndicator } from '@/modules/video-editor/components/common/resource-collection-indicator'
import { cn } from '@/components/lib/utils'
import { EditorDraggableTypes, useTypedDraggable } from '@/modules/video-editor/components/editor-dnd-wrapper'

interface SmartStickerItemProps {
  sticker: PasterResource.Paster | PasterResource.PasterLocal
  onCollectionChange?: (collected: boolean) => void
  className?: string
  isLocal?: boolean
  // onItemAdd: () => void
}

/**
 * 智能贴纸项组件 - 支持三层资源加载策略
 *
 * 第一层：cover.url - 初始显示（静态图片，快速加载）
 * 第二层：thumbUrl - 悬停预览（动态GIF，中等大小）
 * 第三层：fileUrl - 编辑器使用（完整资源，高质量）
 */
export const StickerItemContent: React.FC<SmartStickerItemProps> = memo(
  function SmartStickerItemContent({
    sticker, onCollectionChange, className = '', isLocal = false
  }) {
    const { loadStickerPreview } = useResource()

    // 本地状态
    const [isHovering, setIsHovering] = useState(false)
    const [showPreview, setShowPreview] = useState(false)
    const hoverTimeoutRef = useRef<NodeJS.Timeout>(null)
    const previewTimeoutRef = useRef<NodeJS.Timeout>(null)

    // 获取贴纸加载状态
    const loadingState = useStickerLoadingStore(selectStickerState('fileId' in sticker ? sticker.fileId : sticker.id))

    // 检查文件资源是否正在加载（直接从 loadingState 获取）
    const isFileLoading = loadingState.fileLoading

    // 收藏状态
    const isCollected = sticker.interactInfo?.collected || false

    // 清理定时器
    useEffect(() => {
      return () => {
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current)
        }
        if (previewTimeoutRef.current) {
          clearTimeout(previewTimeoutRef.current)
        }
      }
    }, [])

    // 鼠标进入处理
    const handleMouseEnter = useCallback(() => {
      setIsHovering(true)

      // 清除之前的定时器
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
      }
      if (previewTimeoutRef.current) {
        clearTimeout(previewTimeoutRef.current)
      }

      // 延迟加载预览（避免快速划过时不必要的加载）
      hoverTimeoutRef.current = setTimeout(async () => {
        try {
          await loadStickerPreview(sticker)
          setShowPreview(true)
        } catch (error) {
          console.error('加载贴纸预览失败:', error)
        }
      }, 300) // 300ms延迟
    }, [sticker, loadStickerPreview])

    // 鼠标离开处理
    const handleMouseLeave = useCallback(() => {
      setIsHovering(false)

      // 清除加载定时器
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
      }

      // 延迟隐藏预览（避免闪烁）
      previewTimeoutRef.current = setTimeout(() => {
        setShowPreview(false)
      }, 200) // 200ms延迟
    }, [])

    // 决定显示哪个图片URL
    const getDisplayImageUrl = useCallback(() => {
      // 如果正在悬停且预览已加载，显示thumbUrl
      if (showPreview && loadingState.thumbLoaded) {
        return sticker.content.thumbUrl
      }

      // 否则显示cover.url（静态图片），如果cover为null则使用thumbUrl作为fallback
      return sticker.cover?.url || sticker.content.thumbUrl
    }, [showPreview, loadingState.thumbLoaded, sticker.cover?.url, sticker.content.thumbUrl])

    return (
      <div className={`aspect-square ${className}`} draggable={false}>
        <div
          className={cn(
            `group relative w-full h-full rounded border
           bg-gray-800/40 border-gray-700/10
           hover:border-blue-500/20 hover:bg-blue-500/5
             transition-all overflow-hidden cursor-grab`,
            loadingState.fileLoaded && 'border-green-500/20'
          )}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* 主要图片显示区域 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src={getDisplayImageUrl()}
              alt={sticker.title}
              className={cn(
                'max-w-full max-h-full object-contain transition-opacity duration-200',
                showPreview && loadingState.thumbLoaded ? 'opacity-100' : 'opacity-100'
              )}
              loading="lazy"
            />
          </div>

          {/* 加载指示器 */}
          {loadingState.thumbLoading && isHovering && !isLocal && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/20">
              <div className="text-xs text-white">加载预览...</div>
            </div>
          )}

          {/* 缓存状态指示器 */}
          {!isLocal && (
            <div
              className={cn(
                'absolute right-0 bottom-0 transition-opacity duration-200',
                (loadingState.fileLoaded || isFileLoading) ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
              )}
            >
              <ResourceCacheIndicator
                resourceType={ResourceType.STICKER}
                resourceUrl={sticker.content.fileUrl}
                isLoading={isFileLoading}
                size={12}
                isCached={loadingState.fileLoaded}
                isChecking={false} // 我们已经有了状态，不需要检查
              />
            </div>
          )}

          {/* 收藏状态指示器 */}
          {!isLocal  && (
            <div className={cn(
              'absolute right-0 top-0 transition-opacity duration-200',
              isCollected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
            )}
            >
              <ResourceCollectionIndicator
                resourceType={ResourceType.STICKER}
                resourceId={sticker.id}
                isCollected={isCollected}
                size={12}
                onCollectionChange={onCollectionChange}
              />
            </div>
          )}

          {/* 悬停时显示的预览标识 */}
          {isHovering && !isLocal  && (
            <div className="absolute bottom-1 left-1">
              <div className="text-[10px] bg-blue-500/80 text-white rounded px-1 py-0.5">
                {showPreview && loadingState.thumbLoaded ? '预览' : '悬停预览'}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，确保收藏状态变化时组件会重新渲染
    return (
      prevProps.sticker.id === nextProps.sticker.id &&
      prevProps.sticker.title === nextProps.sticker.title &&
      prevProps.sticker.cover?.url === nextProps.sticker.cover?.url &&
      prevProps.sticker.content.thumbUrl === nextProps.sticker.content.thumbUrl &&
      prevProps.sticker.content.fileUrl === nextProps.sticker.content.fileUrl &&
      prevProps.sticker.interactInfo?.collected === nextProps.sticker.interactInfo?.collected &&
      prevProps.className === nextProps.className
    )
  }
)

export const StickerItem: FC<SmartStickerItemProps> = props => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.Resource,
    props.sticker.id,
    {
      resourceType: CloudResourceTypes.PASTER,
      data: props.sticker
    }
  )

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      <StickerItemContent {...props} />
    </div>
  )
}

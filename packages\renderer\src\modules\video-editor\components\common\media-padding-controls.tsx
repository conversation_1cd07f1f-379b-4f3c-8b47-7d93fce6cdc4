import React from 'react'
import { StickerOverlay, VideoOverlay } from '@clipnest/remotion-shared/types'
import { FormSlider } from './form-components'
import { useOverlayEditing } from '@/modules/video-editor/contexts'

export function MediaPaddingControls() {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay | StickerOverlay>()

  const paddingValue = localOverlay?.styles?.padding || 0
  const paddingBackgroundColor = localOverlay?.styles?.paddingBackgroundColor || 'transparent'

  return (
    <div className="space-y-4">
      {/* Padding Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-xs text-gray-600 dark:text-gray-400">
            内边距(%)
          </label>
        </div>
        <FormSlider
          min={0}
          max={50}
          step={1}
          value={paddingValue}
          onChange={(e, commit) => updateEditingOverlay({ styles: { padding: e } }, commit)}
        />
      </div>

      {/* Padding Background Color */}
      <div className="flex justify-start gap-3 items-center">
        <label className="text-xs text-gray-600 dark:text-gray-400">
          背景颜色
        </label>
        <input
          type="color"
          value={
            paddingBackgroundColor === 'transparent'
              ? '#ffffff'
              : paddingBackgroundColor
          }
          onChange={e => updateEditingOverlay({ styles: { paddingBackgroundColor: e.target.value } }, true)}
          className="w-8 h-8 border border-gray-200 dark:border-gray-700 rounded-md p-0.5 cursor-pointer"
        />
      </div>
    </div>
  )
}

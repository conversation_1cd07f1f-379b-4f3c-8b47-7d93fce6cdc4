import { Injectable, Inject } from '@nestjs/common'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'
import { AutoUpdaterService } from './auto-updater.service.js'

@Injectable()
export class AutoUpdaterIPCHandler extends BaseIPCHandler<'autoUpdater'> {

  protected readonly platformPrefix = 'autoUpdater'

  constructor(@Inject(AutoUpdaterService) private readonly autoUpdaterService: AutoUpdaterService) {
    super()
  }

  /**
   * 注册所有 IPC 处理程序
   */
  registerAll(): void {
    this.registerHandler('checkForUpdates', this.autoUpdaterService.checkForUpdates.bind(this.autoUpdaterService))

    this.registerHandler('startUpdate', this.autoUpdaterService.startUpdate.bind(this.autoUpdaterService))

    this.registerHandler('installUpdate', this.autoUpdaterService.installUpdate.bind(this.autoUpdaterService))
  }
}